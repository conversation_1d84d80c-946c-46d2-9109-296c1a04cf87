<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - Access Welfare Trust</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .welcome-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 3rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-section h1 {
            margin-bottom: 1rem;
            font-size: 2.5rem;
            color: white;
            border-bottom: none;
        }

        .welcome-section p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            border: 1px solid var(--border-color);
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .card-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .card-description {
            color: var(--text-color);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .card-button {
            background: var(--gradient-primary);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-block;
        }

        .card-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(30, 95, 116, 0.3);
        }

        .application-status {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .no-application {
            text-align: center;
            padding: 3rem;
            color: var(--text-color);
        }

        .no-application i {
            font-size: 4rem;
            color: var(--border-color);
            margin-bottom: 1rem;
        }

        .user-info {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            float: right;
        }

        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .welcome-section h1 {
                font-size: 2rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <nav class="nav-links">
                    <a href="index.html">Home</a>
                    <a href="about.html">About Us</a>
                    <a href="eligibility.html">Eligibility</a>
                    <a href="application.html">Apply</a>
                </nav>
                <button id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </header>

    <main class="dashboard-container">
        <div class="welcome-section">
            <h1>Welcome, <span id="userName">Student</span>!</h1>
            <p>Your gateway to educational opportunities and scholarship applications</p>
        </div>

        <div class="user-info">
            <h2>Account Information</h2>
            <p><strong>Name:</strong> <span id="userFullName">Loading...</span></p>
            <p><strong>Email:</strong> <span id="userEmail">Loading...</span></p>
            <p><strong>Member Since:</strong> <span id="memberSince">Loading...</span></p>
        </div>

        <div class="application-status" id="applicationStatus">
            <!-- Application status will be loaded here -->
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3 class="card-title">Apply for Scholarship</h3>
                <p class="card-description">
                    Submit your scholarship application for the academic year 2025-26. 
                    Complete the comprehensive form with all required documents.
                </p>
                <a href="application.html" class="card-button">
                    <i class="fas fa-plus"></i> Start Application
                </a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="card-title">Eligibility Criteria</h3>
                <p class="card-description">
                    Review the eligibility requirements and ensure you meet all 
                    criteria before submitting your application.
                </p>
                <a href="eligibility.html" class="card-button">
                    <i class="fas fa-check"></i> Check Eligibility
                </a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-download"></i>
                </div>
                <h3 class="card-title">Download Documents</h3>
                <p class="card-description">
                    Download application forms, guidelines, and other important 
                    documents related to the scholarship program.
                </p>
                <a href="#" class="card-button">
                    <i class="fas fa-file-pdf"></i> Download Forms
                </a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <h3 class="card-title">Contact Support</h3>
                <p class="card-description">
                    Need help with your application? Contact our support team 
                    for assistance and guidance.
                </p>
                <a href="get-involved.html" class="card-button">
                    <i class="fas fa-envelope"></i> Get Help
                </a>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <p>Access Welfare Trust is dedicated to supporting education for persons with disabilities and those from economically weaker sections.</p>
            </div>
            <div class="footer-section">
                <h3>Contact Us</h3>
                <p><i class="fas fa-phone"></i> +91 9498980331</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
            </div>
        </div>
        <div class="copyright">
            <p>&copy; 2025 Access Welfare Trust. All rights reserved.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            const userSession = localStorage.getItem('userSession');
            if (!userSession) {
                window.location.href = 'login-signup.html';
                return;
            }

            const user = JSON.parse(userSession);
            
            // Redirect admin to admin dashboard
            if (user.role === 'admin') {
                window.location.href = 'admin/dashboard.html';
                return;
            }

            // Update user information
            document.getElementById('userName').textContent = user.full_name.split(' ')[0];
            document.getElementById('userFullName').textContent = user.full_name;
            document.getElementById('userEmail').textContent = user.email;
            
            // Set member since date (you can get this from the database)
            document.getElementById('memberSince').textContent = new Date().toLocaleDateString();

            // Load application status
            loadApplicationStatus();

            // Handle logout
            document.getElementById('logoutBtn').addEventListener('click', async function() {
                try {
                    await fetch('api/logout.php', { method: 'POST' });
                    localStorage.removeItem('userSession');
                    window.location.href = 'login-signup.html';
                } catch (error) {
                    console.error('Logout error:', error);
                    // Force logout even if API call fails
                    localStorage.removeItem('userSession');
                    window.location.href = 'login-signup.html';
                }
            });
        });

        async function loadApplicationStatus() {
            const statusContainer = document.getElementById('applicationStatus');
            
            try {
                // This would typically fetch from an API
                // For now, we'll show a placeholder
                statusContainer.innerHTML = `
                    <div class="no-application">
                        <i class="fas fa-file-alt"></i>
                        <h3>No Application Submitted</h3>
                        <p>You haven't submitted any scholarship application yet. Click "Start Application" to begin.</p>
                    </div>
                `;
            } catch (error) {
                console.error('Error loading application status:', error);
                statusContainer.innerHTML = `
                    <div class="no-application">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Unable to Load Status</h3>
                        <p>There was an error loading your application status. Please try again later.</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
