-- Create database
CREATE DATABASE IF NOT EXISTS awt_scholarship;
USE awt_scholarship;

-- Users table for authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENU<PERSON>('student', 'admin') DEFAULT 'student',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Applications table
CREATE TABLE applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    application_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- Personal Details
    name VARCHAR(255) NOT NULL,
    gender ENUM('Male', 'Female', 'Other') NOT NULL,
    dob DATE NOT NULL,
    age INT NOT NULL,
    birthplace VARCHAR(255) NOT NULL,
    marital_status ENUM('Single', 'Married', 'Divorced', 'Widowed') NOT NULL,
    religion VARCHAR(100) NOT NULL,
    category ENUM('BC', 'MBC', 'SC', 'ST', 'General') NOT NULL,
    nationality VARCHAR(100) NOT NULL,
    present_address TEXT NOT NULL,
    present_state VARCHAR(100) NOT NULL,
    present_country VARCHAR(100) NOT NULL,
    permanent_address TEXT NOT NULL,
    permanent_state VARCHAR(100) NOT NULL,
    permanent_country VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    whatsapp VARCHAR(20),
    email VARCHAR(255) NOT NULL,
    
    -- Family Details
    family_income DECIMAL(10,2) NOT NULL,
    is_disabled ENUM('Yes', 'No') NOT NULL,
    disability_type VARCHAR(255),
    disability_percentage INT,
    disability_description TEXT,
    issuing_authority VARCHAR(255),
    certificate_number VARCHAR(100),
    issue_date VARCHAR(100),
    
    -- Educational Details
    current_course VARCHAR(255) NOT NULL,
    course_duration VARCHAR(100) NOT NULL,
    course_year VARCHAR(50) NOT NULL,
    roll_no VARCHAR(100) NOT NULL,
    institution_name VARCHAR(255) NOT NULL,
    institution_type ENUM('Government', 'Aided', 'Private') NOT NULL,
    institution_address TEXT NOT NULL,
    institution_phone VARCHAR(20) NOT NULL,
    institution_email VARCHAR(255) NOT NULL,
    institution_website VARCHAR(255),
    
    -- Scholarship Details
    term_fees VARCHAR(100) NOT NULL,
    tuition_fees DECIMAL(10,2) NOT NULL,
    other_fees DECIMAL(10,2) NOT NULL,
    amount_figures DECIMAL(10,2) NOT NULL,
    amount_words VARCHAR(500) NOT NULL,
    previous_awt_scholarship ENUM('Yes', 'No'),
    other_scholarships TEXT,
    applied_scholarships TEXT,
    
    -- References and Additional Info
    extracurricular TEXT,
    other_info TEXT,
    goals TEXT NOT NULL,
    place VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    
    -- Application Status
    status ENUM('Pending', 'Approved', 'Rejected') DEFAULT 'Pending',
    admin_notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Family details table
CREATE TABLE family_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    relationship ENUM('Father', 'Mother', 'Spouse', 'Sibling 1', 'Sibling 2', 'Sibling 3', 'Sibling 4') NOT NULL,
    name VARCHAR(255),
    age INT,
    occupation VARCHAR(255),
    income DECIMAL(10,2),
    employment_details VARCHAR(255),
    
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- Educational details table
CREATE TABLE educational_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    education_level ENUM('SSLC', 'HSC', 'UG', 'Vocational', 'Diploma', 'Others') NOT NULL,
    institution VARCHAR(255),
    type ENUM('Government', 'Private'),
    board_university VARCHAR(255),
    marks_grade VARCHAR(100),
    year_of_passing INT,
    
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- References table
CREATE TABLE references (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    reference_number INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    position ENUM('Teacher', 'HoD', 'Principal', 'Other') NOT NULL,
    
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- Document uploads table
CREATE TABLE document_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    document_type VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT INTO users (full_name, email, password_hash, role) VALUES 
('Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');
-- Default password is 'password' - change this in production!

-- Create indexes for better performance
CREATE INDEX idx_applications_user_id ON applications(user_id);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_applications_created_at ON applications(created_at);
CREATE INDEX idx_family_details_application_id ON family_details(application_id);
CREATE INDEX idx_educational_details_application_id ON educational_details(application_id);
CREATE INDEX idx_references_application_id ON references(application_id);
CREATE INDEX idx_document_uploads_application_id ON document_uploads(application_id);
