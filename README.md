# Access Welfare Trust - Scholarship Application System

A complete web application for managing scholarship applications with user authentication, application submission, and admin dashboard.

## Features

### For Students:
- **User Registration & Login**: Secure signup and authentication system
- **Student Dashboard**: Personalized home page after login
- **Application Form**: Comprehensive multi-step scholarship application form
- **Application Tracking**: View application status and details

### For Administrators:
- **Admin Dashboard**: Overview of all applications with statistics
- **Application Management**: View, approve, or reject applications
- **User Management**: Manage student accounts
- **Reports & Analytics**: Track application trends and statistics

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Authentication**: Session-based with password hashing
- **File Uploads**: Support for document attachments

## Installation & Setup

### Prerequisites
- Web server (Apache/Nginx) with PHP 7.4+
- MySQL 5.7+ or MariaDB
- PHP extensions: PDO, PDO_MySQL

### Quick Setup

1. **Clone/Download** the project files to your web server directory

2. **Run the Setup Script**:
   - Navigate to `http://your-domain/setup.php`
   - Follow the step-by-step setup wizard
   - Configure database connection
   - Create database and tables
   - Delete `setup.php` after completion

3. **Default Admin Access**:
   - Email: `<EMAIL>`
   - Password: `password`
   - **⚠️ Change this password immediately after first login!**

## Application Workflow

### 1. User Registration (Signup)
- Students register with full name, email, and secure password
- Password strength validation enforced
- Email uniqueness validation
- Automatic role assignment (student)

### 2. User Authentication (Login)
- Email and password authentication
- Role-based redirection:
  - Students → Student Dashboard (`home.html`)
  - Admins → Admin Dashboard (`admin/dashboard.html`)
- Session management with logout functionality

### 3. Student Dashboard
- Welcome message with user information
- Quick access to application form
- Application status tracking
- Links to eligibility criteria and support

### 4. Application Submission
- **Authentication Required**: Must be logged in to access
- **Multi-step Form**:
  - Step 1: Personal Details
  - Step 2: Family Details
  - Step 3: Educational Details
  - Step 4: Scholarship Details
  - Step 5: Document Upload & Declaration
- **Form Validation**: Client-side and server-side validation
- **Auto-save**: Progress saved in localStorage
- **File Uploads**: Support for documents and photos
- **Unique Application ID**: Generated automatically

### 5. Admin Dashboard
- **Authentication Required**: Admin role required
- **Statistics Overview**: Total, pending, approved, rejected applications
- **Application Management**:
  - View all applications in table format
  - Search and filter functionality
  - Approve/reject applications
  - View detailed application information
- **Real-time Updates**: Dynamic loading of application data

## Database Structure

### Core Tables:
- `users`: User accounts and authentication
- `applications`: Main application data
- `family_details`: Family member information
- `educational_details`: Educational background
- `references`: Reference contacts
- `document_uploads`: File attachments

### Key Features:
- Foreign key relationships for data integrity
- Indexes for performance optimization
- Secure password hashing
- Application status tracking
- Audit trail with timestamps

## Security Features

- **Password Hashing**: bcrypt with salt
- **SQL Injection Protection**: Prepared statements
- **XSS Prevention**: Input sanitization
- **Authentication Checks**: Role-based access control
- **Session Management**: Secure session handling
- **File Upload Security**: Type and size validation

## API Endpoints

### Authentication:
- `POST /api/signup.php` - User registration
- `POST /api/login.php` - User authentication
- `POST /api/logout.php` - User logout

### Applications:
- `POST /api/submit_application.php` - Submit application
- `GET /api/admin_applications.php` - Get applications (admin)
- `POST /api/admin_applications.php` - Update application status (admin)

## Project Structure

```
├── index.html              # Main landing page
├── login-signup.html       # Authentication page
├── home.html              # Student dashboard
├── application.html       # Application form
├── admin/
│   ├── dashboard.html     # Admin dashboard
│   └── view_application.html # Application details
├── api/                   # Backend API endpoints
│   ├── signup.php         # User registration
│   ├── login.php          # User authentication
│   ├── logout.php         # User logout
│   ├── submit_application.php # Application submission
│   └── admin_applications.php # Admin application management
├── config/                # Configuration files
│   └── database.php       # Database configuration
├── database/              # Database schema
│   └── schema.sql         # MySQL database structure
├── includes/              # PHP includes
│   └── auth.php           # Authentication functions
├── css/                   # Stylesheets
│   └── styles.css         # Main stylesheet
├── js/                    # JavaScript files
│   └── main.js            # Application logic
├── images/                # Static images
├── uploads/               # File uploads directory
└── setup.php              # Setup wizard
```

## Manual Setup (Alternative)

1. **Database Setup**:
   ```sql
   -- Import the database schema
   mysql -u username -p < database/schema.sql
   ```

2. **Configuration**:
   ```php
   // Edit config/database.php with your database credentials
   define('DB_HOST', 'localhost');
   define('DB_USERNAME', 'your_username');
   define('DB_PASSWORD', 'your_password');
   define('DB_NAME', 'awt_scholarship');
   ```

3. **Permissions**:
   - Ensure `uploads/` directory is writable
   - Set appropriate file permissions

## Customization

### Styling:
- Edit `css/styles.css` for visual customization
- CSS variables for easy color scheme changes
- Responsive design for mobile compatibility

### Functionality:
- Modify form fields in `application.html`
- Update database schema as needed
- Extend API endpoints for additional features

## Troubleshooting

### Common Issues:

1. **Database Connection Failed**:
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **File Upload Errors**:
   - Check `uploads/` directory permissions
   - Verify PHP upload settings (`upload_max_filesize`, `post_max_size`)

3. **Authentication Issues**:
   - Clear browser localStorage
   - Check session configuration
   - Verify user exists in database

4. **Permission Denied**:
   - Check file/directory permissions
   - Ensure web server has write access to required directories

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Support

For technical support or questions:
- Email: <EMAIL>
- Phone: +91 **********

## License

© 2025 Access Welfare Trust. All rights reserved.
Registration No: 18/Trichy [1]/BK-4/06/2021
