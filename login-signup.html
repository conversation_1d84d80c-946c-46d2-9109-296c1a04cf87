<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login & Sign Up - Access Welfare Trust</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .auth-container {
            max-width: 900px;
            margin: 3rem auto;
            display: flex;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .auth-image {
            flex: 1;
            background-image: url('https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
            background-size: cover;
            background-position: center;
            position: relative;
            min-height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(30, 95, 116, 0.7);
        }

        .auth-image-content {
            position: relative;
            z-index: 1;
            color: white;
            text-align: center;
            padding: 2rem;
        }

        .auth-image-content h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: white;
            border-bottom: none;
        }

        .auth-image-content p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .auth-forms {
            flex: 1;
            background-color: white;
            padding: 3rem 2rem;
        }

        .tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .tab {
            padding: 1rem 2rem;
            cursor: pointer;
            font-weight: 600;
            color: var(--dark-color);
            position: relative;
            transition: var(--transition);
        }

        .tab.active {
            color: var(--primary-color);
        }

        .tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .form-container {
            display: none;
        }

        .form-container.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-family: inherit;
            font-size: 1rem;
            transition: var(--transition);
            background-color: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 95, 116, 0.1);
            background-color: white;
        }

        .form-group input::placeholder {
            color: #adb5bd;
            font-style: italic;
            font-size: 0.9rem;
        }

        .form-group .password-container {
            position: relative;
        }

        .form-group .toggle-password {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
        }

        .auth-button {
            width: 100%;
            padding: 1rem;
            background-image: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-top: 1rem;
            box-shadow: 0 4px 10px rgba(30, 95, 116, 0.2);
        }

        .auth-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(30, 95, 116, 0.3);
        }

        .form-footer {
            text-align: center;
            margin-top: 2rem;
            color: #6c757d;
        }

        .form-footer a {
            color: var(--primary-color);
            font-weight: 500;
        }

        .form-footer a:hover {
            text-decoration: underline;
        }

        .password-requirements {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .password-requirements ul {
            padding-left: 1.5rem;
            margin-top: 0.5rem;
        }

        .password-requirements li {
            margin-bottom: 0.25rem;
        }

        @media (max-width: 768px) {
            .auth-container {
                flex-direction: column;
            }

            .auth-image {
                min-height: 200px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <nav class="nav-links">
                    <a href="index.html">Home</a>
                    <a href="about.html">About Us</a>
                    <a href="eligibility.html">Eligibility</a>
                    <a href="application.html">Apply</a>
                </nav>
                <a href="login-signup.html" class="admin-link active">Login / Sign Up</a>
            </div>
        </div>
    </header>

    <main>
        <div class="auth-container">
            <div class="auth-image">
                <div class="auth-image-content">
                    <h2>Welcome to Access Welfare Trust</h2>
                    <p>Login or create an account to apply for scholarships, track your application status, and more.</p>
                </div>
            </div>
            <div class="auth-forms">
                <div class="tabs">
                    <div class="tab active" data-tab="login">Login</div>
                    <div class="tab" data-tab="signup">Sign Up</div>
                </div>

                <div class="form-container active" id="login-form">
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="login-email">Email</label>
                            <input type="email" id="login-email" name="email" placeholder="Enter your email address" required>
                        </div>

                        <div class="form-group">
                            <label for="login-password">Password</label>
                            <div class="password-container">
                                <input type="password" id="login-password" name="password" placeholder="Enter your password" required>
                                <i class="toggle-password fas fa-eye-slash" data-target="login-password"></i>
                            </div>
                        </div>

                        <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
                            <label style="display: inline-flex; align-items: center; margin: 0;">
                                <input type="checkbox" name="remember" style="width: auto; margin-right: 0.5rem;"> Remember me
                            </label>
                            <a href="#" style="color: var(--primary-color);">Forgot Password?</a>
                        </div>

                        <button type="submit" class="auth-button">Login</button>

                        <div class="form-footer">
                            Don't have an account? <a href="#" class="switch-tab" data-tab="signup">Sign Up</a>
                        </div>
                    </form>
                </div>

                <div class="form-container" id="signup-form">
                    <form id="signupForm">
                        <div class="form-group">
                            <label for="signup-name">Full Name (as in Aadhaar Card)</label>
                            <input type="text" id="signup-name" name="full_name" placeholder="e.g., John Michael Smith" required>
                        </div>

                        <div class="form-group">
                            <label for="signup-email">Email</label>
                            <input type="email" id="signup-email" name="email" placeholder="e.g., <EMAIL>" required>
                        </div>

                        <div class="form-group">
                            <label for="signup-password">Password</label>
                            <div class="password-container">
                                <input type="password" id="signup-password" name="password" placeholder="Create a strong password" required>
                                <i class="toggle-password fas fa-eye-slash" data-target="signup-password"></i>
                            </div>
                            <div class="password-requirements">
                                Password must contain:
                                <ul>
                                    <li>At least 8 characters</li>
                                    <li>At least one uppercase letter</li>
                                    <li>At least one lowercase letter</li>
                                    <li>At least one number</li>
                                    <li>At least one special character</li>
                                </ul>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirm-password">Confirm Password</label>
                            <div class="password-container">
                                <input type="password" id="confirm-password" name="confirm_password" placeholder="Re-enter your password" required>
                                <i class="toggle-password fas fa-eye-slash" data-target="confirm-password"></i>
                            </div>
                        </div>

                        <div class="form-group" style="display: flex; align-items: flex-start;">
                            <input type="checkbox" name="terms_agreed" style="width: auto; margin-right: 0.5rem; margin-top: 0.3rem;" required>
                            <label style="margin: 0;">I agree to the <a href="#" style="color: var(--primary-color);">Terms of Service</a> and <a href="#" style="color: var(--primary-color);">Privacy Policy</a></label>
                        </div>

                        <button type="submit" class="auth-button">Create Account</button>

                        <div class="form-footer">
                            Already have an account? <a href="#" class="switch-tab" data-tab="login">Login</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <p>Access Welfare Trust is dedicated to supporting education for persons with disabilities and those from economically weaker sections.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="eligibility.html">Eligibility</a></li>
                    <li><a href="application.html">Apply Now</a></li>
                    <li><a href="current-projects.html">Current Projects</a></li>
                    <li><a href="accomplished-projects.html">Accomplished Projects</a></li>
                    <li><a href="gallery.html">Gallery</a></li>
                    <li><a href="get-involved.html">Get Involved</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Contact Us</h3>
                <p><i class="fas fa-map-marker-alt"></i> 1/3, Vasanth Nagar, Behind Jeya Nagar, Karumandapam, Tiruchirappalli - 620001</p>
                <p><i class="fas fa-phone"></i> +91 9498980331</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-globe"></i> www.awt.org.in</p>
            </div>
        </div>

        <div class="copyright">
            <p>&copy; 2025 Access Welfare Trust. All rights reserved. | Registration No: 18/Trichy [1]/BK-4/06/2021</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            const userSession = localStorage.getItem('userSession');
            if (userSession) {
                const user = JSON.parse(userSession);
                if (user.role === 'admin') {
                    window.location.href = 'admin/dashboard.html';
                } else {
                    window.location.href = 'home.html';
                }
                return;
            }

            // Tab switching
            const tabs = document.querySelectorAll('.tab');
            const formContainers = document.querySelectorAll('.form-container');
            const switchTabLinks = document.querySelectorAll('.switch-tab');

            function switchTab(tabId) {
                // Update active tab
                tabs.forEach(tab => {
                    if (tab.dataset.tab === tabId) {
                        tab.classList.add('active');
                    } else {
                        tab.classList.remove('active');
                    }
                });

                // Update active form
                formContainers.forEach(form => {
                    if (form.id === tabId + '-form') {
                        form.classList.add('active');
                    } else {
                        form.classList.remove('active');
                    }
                });
            }

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    switchTab(this.dataset.tab);
                });
            });

            switchTabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    switchTab(this.dataset.tab);
                });
            });

            // Password visibility toggle
            const togglePasswordButtons = document.querySelectorAll('.toggle-password');

            togglePasswordButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.dataset.target;
                    const passwordInput = document.getElementById(targetId);

                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        this.classList.remove('fa-eye-slash');
                        this.classList.add('fa-eye');
                    } else {
                        passwordInput.type = 'password';
                        this.classList.remove('fa-eye');
                        this.classList.add('fa-eye-slash');
                    }
                });
            });

            // Login form submission
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;

                submitButton.textContent = 'Logging in...';
                submitButton.disabled = true;

                try {
                    const response = await fetch('api/login.php', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Store user session
                        localStorage.setItem('userSession', JSON.stringify(result.user));

                        // Redirect based on role
                        if (result.user.role === 'admin') {
                            window.location.href = 'admin/dashboard.html';
                        } else {
                            window.location.href = 'home.html';
                        }
                    } else {
                        alert(result.message || 'Login failed. Please try again.');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    alert('An error occurred. Please try again.');
                } finally {
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            });

            // Signup form submission
            const signupForm = document.getElementById('signupForm');
            signupForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const password = formData.get('password');
                const confirmPassword = formData.get('confirm_password');

                // Validate password match
                if (password !== confirmPassword) {
                    alert('Passwords do not match!');
                    return;
                }

                // Validate password strength
                if (!validatePassword(password)) {
                    alert('Password does not meet the requirements!');
                    return;
                }

                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;

                submitButton.textContent = 'Creating Account...';
                submitButton.disabled = true;

                try {
                    const response = await fetch('api/signup.php', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert('Account created successfully! Please login.');
                        switchTab('login');
                        signupForm.reset();
                    } else {
                        alert(result.message || 'Signup failed. Please try again.');
                    }
                } catch (error) {
                    console.error('Signup error:', error);
                    alert('An error occurred. Please try again.');
                } finally {
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            });

            // Password validation function
            function validatePassword(password) {
                const minLength = 8;
                const hasUpperCase = /[A-Z]/.test(password);
                const hasLowerCase = /[a-z]/.test(password);
                const hasNumbers = /\d/.test(password);
                const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

                return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
            }
        });
    </script>
</body>
</html>
