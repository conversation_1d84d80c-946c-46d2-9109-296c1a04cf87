<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Access Welfare Trust</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Dashboard Styles */
        .dashboard-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 180px);
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #2c3e50;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            padding: 0;
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: #34495e;
        }

        .sidebar-menu i {
            margin-right: 1rem;
            width: 20px;
            text-align: center;
        }

        .user-info {
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #34495e;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .user-role {
            font-size: 0.9rem;
            color: #bdc3c7;
        }

        /* Main Content Styles */
        .main-content {
            padding: 2rem;
            background-color: #f5f5f5;
            overflow-y: auto;
        }

        .page-title {
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        /* Stats Cards */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
            color: var(--accent-color);
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-icon {
            float: right;
            font-size: 2.5rem;
            color: #ecf0f1;
        }

        /* Applications Table */
        .applications-container {
            background-color: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .table-title {
            color: var(--primary-color);
            margin: 0;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .search-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            cursor: pointer;
        }

        .applications-table {
            width: 100%;
            border-collapse: collapse;
        }

        .applications-table th,
        .applications-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .applications-table th {
            background-color: #f9f9f9;
            font-weight: 600;
        }

        .applications-table tr:hover {
            background-color: #f5f5f5;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: #ffeeba;
            color: #856404;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-view,
        .btn-approve,
        .btn-reject {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .btn-view {
            background-color: var(--info-color);
            color: white;
        }

        .btn-approve {
            background-color: var(--success-color);
            color: white;
        }

        .btn-reject {
            background-color: var(--error-color);
            color: white;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 1.5rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border: 1px solid var(--border-color);
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Logout Button */
        .logout-button {
            margin-top: auto;
            padding: 1rem 2rem;
            background-color: #c0392b;
            color: white;
            border: none;
            cursor: pointer;
            width: 100%;
            text-align: left;
            display: flex;
            align-items: center;
        }

        .logout-button i {
            margin-right: 1rem;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="../images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <nav class="nav-links">
                    <a href="../index.html">Home</a>
                    <a href="../about.html">About Us</a>
                    <a href="../eligibility.html">Eligibility</a>
                    <a href="../application.html">Apply</a>
                </nav>
                <a href="login.html" class="admin-link active">Admin Dashboard</a>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <aside class="sidebar">
            <div class="user-info">
                <div class="user-name">Admin User</div>
                <div class="user-role">Administrator</div>
            </div>

            <ul class="sidebar-menu">
                <li><a href="#" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="#"><i class="fas fa-file-alt"></i> Applications</a></li>
                <li><a href="#"><i class="fas fa-check-circle"></i> Approved</a></li>
                <li><a href="#"><i class="fas fa-times-circle"></i> Rejected</a></li>
                <li><a href="#"><i class="fas fa-users"></i> Applicants</a></li>
                <li><a href="#"><i class="fas fa-chart-bar"></i> Reports</a></li>
                <li><a href="#"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>

            <button id="logoutButton" class="logout-button">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </aside>

        <main class="main-content">
            <h1 class="page-title">Dashboard</h1>

            <div class="stats-container">
                <div class="stat-card">
                    <i class="fas fa-file-alt stat-icon"></i>
                    <div class="stat-title">Total Applications</div>
                    <div class="stat-value">125</div>
                    <div class="stat-change">+12% from last month</div>
                </div>

                <div class="stat-card">
                    <i class="fas fa-check-circle stat-icon"></i>
                    <div class="stat-title">Approved</div>
                    <div class="stat-value">78</div>
                    <div class="stat-change">62.4% approval rate</div>
                </div>

                <div class="stat-card">
                    <i class="fas fa-clock stat-icon"></i>
                    <div class="stat-title">Pending</div>
                    <div class="stat-value">32</div>
                    <div class="stat-change">25.6% of total</div>
                </div>

                <div class="stat-card">
                    <i class="fas fa-times-circle stat-icon"></i>
                    <div class="stat-title">Rejected</div>
                    <div class="stat-value">15</div>
                    <div class="stat-change">12% of total</div>
                </div>
            </div>

            <div class="applications-container">
                <div class="table-header">
                    <h2 class="table-title">Recent Applications</h2>

                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search applications...">
                        <button class="search-button"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <table class="applications-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Applicant Name</th>
                            <th>Course</th>
                            <th>Amount</th>
                            <th>Date Applied</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="applicationsTableBody">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>

                <div class="pagination">
                    <button>1</button>
                    <button class="active">2</button>
                    <button>3</button>
                    <button>4</button>
                    <button>5</button>
                </div>
            </div>
        </main>
    </div>

    <footer>
        <p>&copy; 2025 Access Welfare Trust. All rights reserved.</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in and is admin
            const userSession = localStorage.getItem('userSession');
            if (!userSession) {
                window.location.href = '../login-signup.html';
                return;
            }

            const user = JSON.parse(userSession);
            if (user.role !== 'admin') {
                alert('Access denied. Admin privileges required.');
                window.location.href = '../home.html';
                return;
            }

            // Update user info
            document.querySelector('.user-name').textContent = user.full_name;

            // Handle logout
            const logoutButton = document.getElementById('logoutButton');
            if (logoutButton) {
                logoutButton.addEventListener('click', async function() {
                    try {
                        await fetch('../api/logout.php', { method: 'POST' });
                        localStorage.removeItem('userSession');
                        window.location.href = '../login-signup.html';
                    } catch (error) {
                        localStorage.removeItem('userSession');
                        window.location.href = '../login-signup.html';
                    }
                });
            }

            // Load applications data
            loadApplications();
            loadStatistics();

            // Handle search
            const searchButton = document.querySelector('.search-button');
            const searchInput = document.querySelector('.search-input');

            if (searchButton && searchInput) {
                searchButton.addEventListener('click', function() {
                    loadApplications(1, searchInput.value);
                });

                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        loadApplications(1, searchInput.value);
                    }
                });
            }
        });

        async function loadApplications(page = 1, search = '') {
            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: 10,
                    search: search
                });

                const response = await fetch(`../api/admin_applications.php?${params}`);
                const result = await response.json();

                if (result.success) {
                    populateApplicationsTable(result.applications);
                    updatePagination(result.page, result.total_pages);
                } else {
                    console.error('Failed to load applications:', result.message);
                }
            } catch (error) {
                console.error('Error loading applications:', error);
            }
        }

        function populateApplicationsTable(applications) {
            const tableBody = document.getElementById('applicationsTableBody');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            applications.forEach(app => {
                const row = document.createElement('tr');

                // Determine status class
                let statusClass = '';
                switch(app.status) {
                    case 'Pending':
                        statusClass = 'status-pending';
                        break;
                    case 'Approved':
                        statusClass = 'status-approved';
                        break;
                    case 'Rejected':
                        statusClass = 'status-rejected';
                        break;
                }

                row.innerHTML = `
                    <td>${app.application_id}</td>
                    <td>${app.name}</td>
                    <td>${app.course}</td>
                    <td>${app.amount}</td>
                    <td>${app.date}</td>
                    <td><span class="status-badge ${statusClass}">${app.status}</span></td>
                    <td class="action-buttons">
                        <button class="btn-view" onclick="viewApplication(${app.id})">View</button>
                        ${app.status === 'Pending' ? `
                            <button class="btn-approve" onclick="updateStatus(${app.id}, 'Approved')">Approve</button>
                            <button class="btn-reject" onclick="updateStatus(${app.id}, 'Rejected')">Reject</button>
                        ` : ''}
                    </td>
                `;

                tableBody.appendChild(row);
            });
        }

        async function updateStatus(applicationId, status) {
            if (!confirm(`Are you sure you want to ${status.toLowerCase()} this application?`)) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('application_id', applicationId);
                formData.append('status', status);

                const response = await fetch('../api/admin_applications.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert('Application status updated successfully!');
                    loadApplications(); // Reload the table
                    loadStatistics(); // Update statistics
                } else {
                    alert('Failed to update status: ' + result.message);
                }
            } catch (error) {
                console.error('Error updating status:', error);
                alert('An error occurred while updating the status.');
            }
        }

        function viewApplication(applicationId) {
            // Redirect to view application page
            window.location.href = `view_application.html?id=${applicationId}`;
        }

        function updatePagination(currentPage, totalPages) {
            const pagination = document.querySelector('.pagination');
            if (!pagination) return;

            pagination.innerHTML = '';

            // Previous button
            if (currentPage > 1) {
                const prevBtn = document.createElement('button');
                prevBtn.textContent = 'Previous';
                prevBtn.onclick = () => loadApplications(currentPage - 1);
                pagination.appendChild(prevBtn);
            }

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === currentPage ? 'active' : '';
                pageBtn.onclick = () => loadApplications(i);
                pagination.appendChild(pageBtn);
            }

            // Next button
            if (currentPage < totalPages) {
                const nextBtn = document.createElement('button');
                nextBtn.textContent = 'Next';
                nextBtn.onclick = () => loadApplications(currentPage + 1);
                pagination.appendChild(nextBtn);
            }
        }

        async function loadStatistics() {
            try {
                // This would typically be a separate API endpoint
                // For now, we'll calculate from the applications data
                const response = await fetch('../api/admin_applications.php?limit=1000');
                const result = await response.json();

                if (result.success) {
                    const apps = result.applications;
                    const total = apps.length;
                    const approved = apps.filter(app => app.status === 'Approved').length;
                    const pending = apps.filter(app => app.status === 'Pending').length;
                    const rejected = apps.filter(app => app.status === 'Rejected').length;

                    // Update statistics cards
                    document.querySelector('.stat-card:nth-child(1) .stat-value').textContent = total;
                    document.querySelector('.stat-card:nth-child(2) .stat-value').textContent = approved;
                    document.querySelector('.stat-card:nth-child(3) .stat-value').textContent = pending;
                    document.querySelector('.stat-card:nth-child(4) .stat-value').textContent = rejected;
                }
            } catch (error) {
                console.error('Error loading statistics:', error);
            }
        }
    </script>
</body>
</html>
