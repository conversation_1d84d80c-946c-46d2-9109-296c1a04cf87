<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/auth.php';

// Require admin access
requireAdmin();

try {
    $pdo = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Fetch all applications
        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 10);
        $search = sanitizeInput($_GET['search'] ?? '');
        $status = sanitizeInput($_GET['status'] ?? '');
        
        $offset = ($page - 1) * $limit;
        
        // Build query
        $whereConditions = [];
        $params = [];
        
        if (!empty($search)) {
            $whereConditions[] = "(a.name LIKE ? OR a.email LIKE ? OR a.application_id LIKE ? OR a.current_course LIKE ?)";
            $searchTerm = "%{$search}%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }
        
        if (!empty($status)) {
            $whereConditions[] = "a.status = ?";
            $params[] = $status;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM applications a {$whereClause}";
        $stmt = $pdo->prepare($countQuery);
        $stmt->execute($params);
        $totalCount = $stmt->fetch()['total'];
        
        // Get applications
        $query = "
            SELECT 
                a.id,
                a.application_id,
                a.name,
                a.email,
                a.current_course,
                a.amount_figures,
                a.status,
                a.created_at,
                u.full_name as user_name
            FROM applications a
            JOIN users u ON a.user_id = u.id
            {$whereClause}
            ORDER BY a.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $applications = $stmt->fetchAll();
        
        // Format the data
        $formattedApplications = array_map(function($app) {
            return [
                'id' => $app['id'],
                'application_id' => $app['application_id'],
                'name' => $app['name'],
                'email' => $app['email'],
                'course' => $app['current_course'],
                'amount' => '₹' . number_format($app['amount_figures'], 0),
                'status' => $app['status'],
                'date' => date('d-m-Y', strtotime($app['created_at']))
            ];
        }, $applications);
        
        echo json_encode([
            'success' => true,
            'applications' => $formattedApplications,
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($totalCount / $limit)
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Update application status
        $applicationId = intval($_POST['application_id'] ?? 0);
        $status = sanitizeInput($_POST['status'] ?? '');
        $adminNotes = sanitizeInput($_POST['admin_notes'] ?? '');
        
        if (!$applicationId || !in_array($status, ['Pending', 'Approved', 'Rejected'])) {
            echo json_encode(['success' => false, 'message' => 'Invalid parameters']);
            exit;
        }
        
        $stmt = $pdo->prepare("UPDATE applications SET status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$status, $adminNotes, $applicationId]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => 'Application status updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Application not found or no changes made']);
        }
    }
    
} catch (Exception $e) {
    error_log("Admin applications API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
