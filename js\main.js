document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    initializeAnimations();

    // Form navigation
    const form = document.getElementById('scholarshipForm');
    if (!form) return;

    // Calculate age from date of birth
    const dobInput = document.getElementById('dob');
    const ageInput = document.getElementById('age');

    if (dobInput && ageInput) {
        dobInput.addEventListener('change', function() {
            const dob = new Date(this.value);
            const today = new Date();
            let age = today.getFullYear() - dob.getFullYear();
            const monthDiff = today.getMonth() - dob.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
                age--;
            }

            ageInput.value = age;
        });
    }

    // Toggle disability details based on selection
    const isDisabledSelect = document.getElementById('isDisabled');
    const disabilityDetails = document.getElementById('disabilityDetails');

    if (isDisabledSelect && disabilityDetails) {
        isDisabledSelect.addEventListener('change', function() {
            if (this.value === 'Yes') {
                disabilityDetails.style.display = 'block';
            } else {
                disabilityDetails.style.display = 'none';
            }
        });
    }

    // Handle next button clicks
    const nextButtons = document.querySelectorAll('.btn-next');
    nextButtons.forEach(button => {
        button.addEventListener('click', function() {
            const currentStep = this.closest('.form-step');
            const nextStepNum = this.dataset.next;

            // Validate current step
            if (validateStep(currentStep)) {
                // Save form data to localStorage
                saveFormData();

                // Hide current step
                currentStep.style.display = 'none';

                // Show next step
                const nextStep = document.getElementById(`step${nextStepNum}`);
                if (nextStep) {
                    nextStep.style.display = 'block';

                    // Update progress indicator
                    updateProgress(nextStepNum);

                    // Scroll to top
                    window.scrollTo(0, 0);
                }
            }
        });
    });

    // Handle previous button clicks
    const prevButtons = document.querySelectorAll('.btn-prev');
    prevButtons.forEach(button => {
        button.addEventListener('click', function() {
            const currentStep = this.closest('.form-step');
            const prevStepNum = this.dataset.prev;

            // Hide current step
            currentStep.style.display = 'none';

            // Show previous step
            const prevStep = document.getElementById(`step${prevStepNum}`);
            if (prevStep) {
                prevStep.style.display = 'block';

                // Update progress indicator
                updateProgress(prevStepNum);

                // Scroll to top
                window.scrollTo(0, 0);
            }
        });
    });

    // Photo upload preview
    const photoInput = document.getElementById('photo');
    const photoPlaceholder = document.querySelector('.photo-placeholder');

    if (photoInput && photoPlaceholder) {
        photoInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Remove placeholder text
                    while (photoPlaceholder.firstChild) {
                        photoPlaceholder.removeChild(photoPlaceholder.firstChild);
                    }

                    // Create image element
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.maxWidth = '100%';
                    img.style.maxHeight = '100%';
                    photoPlaceholder.appendChild(img);

                    // Add change photo button
                    const label = document.createElement('label');
                    label.setAttribute('for', 'photo');
                    label.textContent = 'Change Photo';
                    label.style.marginTop = '10px';
                    photoPlaceholder.appendChild(label);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Handle file input labels
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        if (input.id !== 'photo') { // Skip photo input as it's handled separately
            input.addEventListener('change', function() {
                const label = this.previousElementSibling;
                if (this.files.length > 0) {
                    if (this.files.length === 1) {
                        label.textContent = `Selected: ${this.files[0].name}`;
                    } else {
                        label.textContent = `Selected: ${this.files.length} files`;
                    }
                } else {
                    label.textContent = label.getAttribute('data-default-text') || 'Choose file';
                }
            });

            // Store original label text
            const label = input.previousElementSibling;
            if (label) {
                label.setAttribute('data-default-text', label.textContent);
            }
        }
    });

    // Form submission
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Check if user is logged in
            const userSession = localStorage.getItem('userSession');
            if (!userSession) {
                alert('Please login to submit your application.');
                window.location.href = 'login-signup.html';
                return;
            }

            // Validate all steps
            let isValid = true;
            const formSteps = document.querySelectorAll('.form-step');

            formSteps.forEach(step => {
                if (!validateStep(step)) {
                    isValid = false;
                }
            });

            if (isValid) {
                const submitButton = form.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;

                submitButton.textContent = 'Submitting Application...';
                submitButton.disabled = true;

                try {
                    // Collect form data
                    const formData = new FormData(form);

                    // Submit to backend
                    const response = await fetch('api/submit_application.php', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Clear saved form data
                        localStorage.removeItem('scholarshipFormData');

                        // Show success message
                        alert('Application submitted successfully! Your application ID is: ' + result.application_id);

                        // Redirect to confirmation page
                        window.location.href = 'confirmation.html';
                    } else {
                        alert(result.message || 'Application submission failed. Please try again.');
                    }
                } catch (error) {
                    console.error('Submission error:', error);
                    alert('An error occurred while submitting your application. Please try again.');
                } finally {
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            }
        });
    }

    // Load saved form data if available
    loadFormData();

    // Helper functions
    function updateProgress(stepNum) {
        const steps = document.querySelectorAll('.step');
        steps.forEach(step => {
            if (parseInt(step.dataset.step) <= parseInt(stepNum)) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
    }

    function validateStep(step) {
        // Get all required inputs in the current step
        const requiredInputs = step.querySelectorAll('[required]');
        let isValid = true;

        requiredInputs.forEach(input => {
            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('error');

                // Add error message if it doesn't exist
                let errorMsg = input.nextElementSibling;
                if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                    errorMsg = document.createElement('div');
                    errorMsg.classList.add('error-message');
                    errorMsg.textContent = 'This field is required';
                    errorMsg.style.color = 'red';
                    errorMsg.style.fontSize = '0.8rem';
                    errorMsg.style.marginTop = '0.25rem';
                    input.parentNode.insertBefore(errorMsg, input.nextSibling);
                }
            } else {
                input.classList.remove('error');

                // Remove error message if it exists
                const errorMsg = input.nextElementSibling;
                if (errorMsg && errorMsg.classList.contains('error-message')) {
                    errorMsg.remove();
                }
            }
        });

        return isValid;
    }

    function saveFormData() {
        // Get all form inputs except file inputs
        const inputs = form.querySelectorAll('input:not([type="file"]), select, textarea');
        const formData = {};

        inputs.forEach(input => {
            if (input.type === 'radio' || input.type === 'checkbox') {
                if (input.checked) {
                    formData[input.name] = input.value;
                }
            } else {
                formData[input.name] = input.value;
            }
        });

        localStorage.setItem('scholarshipFormData', JSON.stringify(formData));
    }

    function loadFormData() {
        const savedData = localStorage.getItem('scholarshipFormData');
        if (savedData) {
            const formData = JSON.parse(savedData);

            // Populate form fields
            Object.keys(formData).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    if (input.type === 'radio' || input.type === 'checkbox') {
                        if (input.value === formData[key]) {
                            input.checked = true;
                        }
                    } else {
                        input.value = formData[key];
                    }
                }
            });

            // If disability is set to Yes, show the disability details
            if (isDisabledSelect && isDisabledSelect.value === 'Yes' && disabilityDetails) {
                disabilityDetails.style.display = 'block';
            }
        }
    }

    // Animation functions
    function initializeAnimations() {
        // Add staggered animation delays to navigation links
        const navLinks = document.querySelectorAll('.nav-links a');
        navLinks.forEach((link, index) => {
            link.style.setProperty('--i', index);
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');

                    // Add staggered animations to child elements
                    const children = entry.target.querySelectorAll('.quick-link-card, .gallery-item, .project-item');
                    children.forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('animate-in');
                        }, index * 200);
                    });
                }
            });
        }, observerOptions);

        // Observe elements for scroll animations
        const animatedElements = document.querySelectorAll('section, .quick-link-card, .gallery-item, .project-item');
        animatedElements.forEach(el => {
            observer.observe(el);
        });

        // Add hover animations to buttons
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, .admin-link');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.animation = 'pulse 0.6s ease-out';
            });

            button.addEventListener('mouseleave', function() {
                this.style.animation = '';
            });
        });

        // Add click animation to cards
        const cards = document.querySelectorAll('.quick-link-card, .gallery-item, .value-card, .contact-card');
        cards.forEach(card => {
            card.addEventListener('click', function() {
                this.style.animation = 'bounce 0.6s ease-out';
                setTimeout(() => {
                    this.style.animation = '';
                }, 600);
            });
        });

        // Parallax effect for hero section
        const hero = document.querySelector('.hero');
        if (hero) {
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;
                hero.style.transform = `translateY(${rate}px)`;
            });
        }

        // Add more animations to various elements
        addAdvancedAnimations();

        // Add typing effect to hero text
        const heroTitle = document.querySelector('.hero h2');
        if (heroTitle) {
            const text = heroTitle.textContent;
            heroTitle.textContent = '';
            heroTitle.style.borderRight = '2px solid white';

            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    heroTitle.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                } else {
                    setTimeout(() => {
                        heroTitle.style.borderRight = 'none';
                    }, 1000);
                }
            };

            setTimeout(typeWriter, 1500);
        }

        // Add smooth scroll for anchor links
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Advanced animations function
    function addAdvancedAnimations() {
        // Animate footer elements on scroll
        const footerSections = document.querySelectorAll('.footer-section');
        footerSections.forEach((section, index) => {
            section.style.animation = `slideInUp 0.8s ease-out ${index * 0.2}s both`;
        });

        // Add wave animation to page headers
        const pageHeaders = document.querySelectorAll('.page-header h1');
        pageHeaders.forEach(header => {
            header.addEventListener('mouseenter', function() {
                this.style.animation = 'shake 0.5s ease-out';
                setTimeout(() => {
                    this.style.animation = '';
                }, 500);
            });
        });

        // Animate contact info items
        const contactItems = document.querySelectorAll('.contact-info p');
        contactItems.forEach((item, index) => {
            item.style.animation = `slideInLeft 0.6s ease-out ${index * 0.1}s both`;
        });

        // Add ripple effect to buttons
        const allButtons = document.querySelectorAll('button, .btn-primary, .btn-secondary, .admin-link');
        allButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Animate form labels
        const formLabels = document.querySelectorAll('label');
        formLabels.forEach((label, index) => {
            label.style.animation = `fadeIn 0.5s ease-out ${index * 0.05}s both`;
        });

        // Add progress bar animation
        const progressSteps = document.querySelectorAll('.step');
        progressSteps.forEach((step, index) => {
            step.style.animation = `scaleIn 0.5s ease-out ${index * 0.1}s both`;
        });

        // Animate social links
        const socialLinks = document.querySelectorAll('.social-links a');
        socialLinks.forEach((link, index) => {
            link.style.animation = `rotateIn 0.6s ease-out ${index * 0.1}s both`;

            link.addEventListener('mouseenter', function() {
                this.style.animation = 'bounce 0.6s ease-out';
            });

            link.addEventListener('mouseleave', function() {
                this.style.animation = '';
            });
        });

        // Add text animation to paragraphs
        const paragraphs = document.querySelectorAll('p');
        paragraphs.forEach((p, index) => {
            if (index % 2 === 0) {
                p.style.animation = `slideInLeft 0.8s ease-out ${index * 0.1}s both`;
            } else {
                p.style.animation = `slideInRight 0.8s ease-out ${index * 0.1}s both`;
            }
        });

        // Animate list items
        const listItems = document.querySelectorAll('li');
        listItems.forEach((item, index) => {
            item.style.animation = `slideInUp 0.5s ease-out ${index * 0.05}s both`;
        });

        // Add hover effects to images (excluding gallery images)
        const images = document.querySelectorAll('img:not(.logo):not(.gallery-image)');
        images.forEach(img => {
            img.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.transition = 'transform 0.3s ease-out';
            });

            img.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Animate table rows
        const tableRows = document.querySelectorAll('tr');
        tableRows.forEach((row, index) => {
            row.style.animation = `slideInLeft 0.5s ease-out ${index * 0.1}s both`;
        });

        // Add floating animation to icons
        const icons = document.querySelectorAll('i');
        icons.forEach((icon, index) => {
            setTimeout(() => {
                icon.style.animation = 'float 3s ease-in-out infinite';
                icon.style.animationDelay = `${index * 0.2}s`;
            }, 2000);
        });

        // Animate value cards
        const valueCards = document.querySelectorAll('.value-card');
        valueCards.forEach((card, index) => {
            card.style.animation = `flipIn 0.8s ease-out ${index * 0.2}s both`;
        });

        // Add scroll-triggered number counting animation
        const numbers = document.querySelectorAll('.impact-number');
        numbers.forEach(number => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = parseInt(entry.target.textContent);
                        let current = 0;
                        const increment = target / 50;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            entry.target.textContent = Math.floor(current);
                        }, 50);
                        observer.unobserve(entry.target);
                    }
                });
            });
            observer.observe(number);
        });

        // Add typewriter effect to taglines
        const taglines = document.querySelectorAll('.tagline');
        taglines.forEach((tagline, index) => {
            setTimeout(() => {
                const text = tagline.textContent;
                tagline.textContent = '';
                let i = 0;
                const typeWriter = () => {
                    if (i < text.length) {
                        tagline.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 50);
                    }
                };
                typeWriter();
            }, 1000 + index * 500);
        });
    }
});
