<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/auth.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Require user to be logged in
requireLogin();

try {
    $pdo = getDBConnection();
    $userId = $_SESSION['user_id'];
    
    // Check if user already has a pending or approved application
    $stmt = $pdo->prepare("SELECT id, status FROM applications WHERE user_id = ? AND status IN ('Pending', 'Approved')");
    $stmt->execute([$userId]);
    $existingApp = $stmt->fetch();
    
    if ($existingApp) {
        echo json_encode([
            'success' => false, 
            'message' => 'You already have a ' . strtolower($existingApp['status']) . ' application. Multiple applications are not allowed.'
        ]);
        exit;
    }
    
    // Generate unique application ID
    do {
        $applicationId = generateApplicationId();
        $stmt = $pdo->prepare("SELECT id FROM applications WHERE application_id = ?");
        $stmt->execute([$applicationId]);
    } while ($stmt->fetch());
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Insert main application data
    $stmt = $pdo->prepare("
        INSERT INTO applications (
            user_id, application_id, name, gender, dob, age, birthplace, marital_status, 
            religion, category, nationality, present_address, present_state, present_country,
            permanent_address, permanent_state, permanent_country, phone, whatsapp, email,
            family_income, is_disabled, disability_type, disability_percentage, disability_description,
            issuing_authority, certificate_number, issue_date, current_course, course_duration,
            course_year, roll_no, institution_name, institution_type, institution_address,
            institution_phone, institution_email, institution_website, term_fees, tuition_fees,
            other_fees, amount_figures, amount_words, previous_awt_scholarship, other_scholarships,
            applied_scholarships, extracurricular, other_info, goals, place, date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $userId,
        $applicationId,
        sanitizeInput($_POST['name'] ?? ''),
        sanitizeInput($_POST['gender'] ?? ''),
        sanitizeInput($_POST['dob'] ?? ''),
        intval($_POST['age'] ?? 0),
        sanitizeInput($_POST['birthplace'] ?? ''),
        sanitizeInput($_POST['maritalStatus'] ?? ''),
        sanitizeInput($_POST['religion'] ?? ''),
        sanitizeInput($_POST['category'] ?? ''),
        sanitizeInput($_POST['nationality'] ?? ''),
        sanitizeInput($_POST['presentAddress'] ?? ''),
        sanitizeInput($_POST['presentState'] ?? ''),
        sanitizeInput($_POST['presentCountry'] ?? ''),
        sanitizeInput($_POST['permanentAddress'] ?? ''),
        sanitizeInput($_POST['permanentState'] ?? ''),
        sanitizeInput($_POST['permanentCountry'] ?? ''),
        sanitizeInput($_POST['phone'] ?? ''),
        sanitizeInput($_POST['whatsapp'] ?? ''),
        sanitizeInput($_POST['email'] ?? ''),
        floatval($_POST['familyIncome'] ?? 0),
        sanitizeInput($_POST['isDisabled'] ?? ''),
        sanitizeInput($_POST['disability_type'] ?? ''),
        intval($_POST['disability_percentage'] ?? 0),
        sanitizeInput($_POST['disability_description'] ?? ''),
        sanitizeInput($_POST['issuingAuthority'] ?? ''),
        sanitizeInput($_POST['certificateNumber'] ?? ''),
        sanitizeInput($_POST['issueDate'] ?? ''),
        sanitizeInput($_POST['currentCourse'] ?? ''),
        sanitizeInput($_POST['courseDuration'] ?? ''),
        sanitizeInput($_POST['courseYear'] ?? ''),
        sanitizeInput($_POST['rollNo'] ?? ''),
        sanitizeInput($_POST['institutionName'] ?? ''),
        sanitizeInput($_POST['institutionType'] ?? ''),
        sanitizeInput($_POST['institutionAddress'] ?? ''),
        sanitizeInput($_POST['institutionPhone'] ?? ''),
        sanitizeInput($_POST['institutionEmail'] ?? ''),
        sanitizeInput($_POST['institutionWebsite'] ?? ''),
        sanitizeInput($_POST['termFees'] ?? ''),
        floatval($_POST['tuitionFees'] ?? 0),
        floatval($_POST['otherFees'] ?? 0),
        floatval($_POST['amountFigures'] ?? 0),
        sanitizeInput($_POST['amountWords'] ?? ''),
        sanitizeInput($_POST['previousAWTScholarship'] ?? ''),
        sanitizeInput($_POST['otherScholarships'] ?? ''),
        sanitizeInput($_POST['appliedScholarships'] ?? ''),
        sanitizeInput($_POST['extracurricular'] ?? ''),
        sanitizeInput($_POST['otherInfo'] ?? ''),
        sanitizeInput($_POST['goals'] ?? ''),
        sanitizeInput($_POST['place'] ?? ''),
        sanitizeInput($_POST['date'] ?? '')
    ]);
    
    $applicationDbId = $pdo->lastInsertId();
    
    // Insert family details
    $familyRelations = ['Father', 'Mother', 'Spouse', 'Sibling 1', 'Sibling 2', 'Sibling 3', 'Sibling 4'];
    foreach ($familyRelations as $relation) {
        $relationKey = strtolower(str_replace(' ', '', $relation));
        if ($relation === 'Sibling 1') $relationKey = 'sibling1';
        if ($relation === 'Sibling 2') $relationKey = 'sibling2';
        if ($relation === 'Sibling 3') $relationKey = 'sibling3';
        if ($relation === 'Sibling 4') $relationKey = 'sibling4';
        
        $name = sanitizeInput($_POST[$relationKey . '_name'] ?? '');
        if (!empty($name)) {
            $stmt = $pdo->prepare("
                INSERT INTO family_details (application_id, relationship, name, age, occupation, income, employment_details)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $applicationDbId,
                $relation,
                $name,
                intval($_POST[$relationKey . '_age'] ?? 0),
                sanitizeInput($_POST[$relationKey . '_occupation'] ?? ''),
                floatval($_POST[$relationKey . '_income'] ?? 0),
                sanitizeInput($_POST[$relationKey . '_employment'] ?? '')
            ]);
        }
    }
    
    // Insert educational details
    $educationLevels = ['sslc', 'hsc', 'ug', 'vocational', 'diploma', 'others'];
    $educationMapping = [
        'sslc' => 'SSLC',
        'hsc' => 'HSC',
        'ug' => 'UG',
        'vocational' => 'Vocational',
        'diploma' => 'Diploma',
        'others' => 'Others'
    ];
    
    foreach ($educationLevels as $level) {
        $institution = sanitizeInput($_POST[$level . '_institution'] ?? '');
        if (!empty($institution)) {
            $stmt = $pdo->prepare("
                INSERT INTO educational_details (application_id, education_level, institution, type, board_university, marks_grade, year_of_passing)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $applicationDbId,
                $educationMapping[$level],
                $institution,
                sanitizeInput($_POST[$level . '_type'] ?? ''),
                sanitizeInput($_POST[$level . '_board'] ?? ''),
                sanitizeInput($_POST[$level . '_marks'] ?? ''),
                intval($_POST[$level . '_year'] ?? 0)
            ]);
        }
    }
    
    // Insert references
    for ($i = 1; $i <= 2; $i++) {
        $refName = sanitizeInput($_POST["reference{$i}_name"] ?? '');
        if (!empty($refName)) {
            $stmt = $pdo->prepare("
                INSERT INTO references (application_id, reference_number, name, phone, email, position)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $applicationDbId,
                $i,
                $refName,
                sanitizeInput($_POST["reference{$i}_phone"] ?? ''),
                sanitizeInput($_POST["reference{$i}_email"] ?? ''),
                sanitizeInput($_POST["reference{$i}_position"] ?? '')
            ]);
        }
    }
    
    // Handle file uploads (simplified - in production, implement proper file handling)
    $uploadDir = '../uploads/' . $applicationId . '/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Application submitted successfully!',
        'application_id' => $applicationId
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    error_log("Application submission error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Application submission failed. Please try again.']);
}
?>
