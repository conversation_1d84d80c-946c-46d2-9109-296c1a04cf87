<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Application - Admin Dashboard</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .application-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .application-header {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--box-shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .application-info h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .application-details {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--box-shadow);
        }

        .section-title {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .detail-item {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
        }

        .detail-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .detail-value {
            color: var(--text-color);
        }

        .status-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .action-button {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-block;
        }

        .btn-approve {
            background: #28a745;
            color: white;
        }

        .btn-reject {
            background: #dc3545;
            color: white;
        }

        .btn-back {
            background: var(--primary-color);
            color: white;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .table-responsive {
            overflow-x: auto;
            margin: 1rem 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        .data-table th,
        .data-table td {
            padding: 0.8rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--text-color);
        }

        .error {
            text-align: center;
            padding: 3rem;
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .application-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .status-actions {
                flex-direction: column;
                width: 100%;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="../images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <a href="dashboard.html" class="admin-link">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </header>

    <main class="application-container">
        <div id="loadingMessage" class="loading">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p>Loading application details...</p>
        </div>

        <div id="errorMessage" class="error" style="display: none;">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
            <p>Failed to load application details.</p>
        </div>

        <div id="applicationContent" style="display: none;">
            <div class="application-header">
                <div class="application-info">
                    <h1 id="applicationTitle">Application Details</h1>
                    <p id="applicationId">Loading...</p>
                </div>
                <div class="status-actions">
                    <span id="statusBadge" class="status-badge">Loading...</span>
                    <div id="actionButtons">
                        <!-- Action buttons will be populated here -->
                    </div>
                </div>
            </div>

            <div class="application-details">
                <h2 class="section-title">Personal Information</h2>
                <div class="detail-grid" id="personalDetails">
                    <!-- Personal details will be populated here -->
                </div>
            </div>

            <div class="application-details">
                <h2 class="section-title">Educational Information</h2>
                <div class="detail-grid" id="educationalDetails">
                    <!-- Educational details will be populated here -->
                </div>
            </div>

            <div class="application-details">
                <h2 class="section-title">Scholarship Information</h2>
                <div class="detail-grid" id="scholarshipDetails">
                    <!-- Scholarship details will be populated here -->
                </div>
            </div>

            <div class="application-details">
                <h2 class="section-title">Family Details</h2>
                <div class="table-responsive">
                    <table class="data-table" id="familyTable">
                        <thead>
                            <tr>
                                <th>Relationship</th>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Occupation</th>
                                <th>Income</th>
                            </tr>
                        </thead>
                        <tbody id="familyTableBody">
                            <!-- Family details will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="application-details">
                <h2 class="section-title">References</h2>
                <div class="table-responsive">
                    <table class="data-table" id="referencesTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Position</th>
                            </tr>
                        </thead>
                        <tbody id="referencesTableBody">
                            <!-- References will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check admin authentication
            const userSession = localStorage.getItem('userSession');
            if (!userSession) {
                window.location.href = '../login-signup.html';
                return;
            }

            const user = JSON.parse(userSession);
            if (user.role !== 'admin') {
                alert('Access denied. Admin privileges required.');
                window.location.href = '../home.html';
                return;
            }

            // Get application ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const applicationId = urlParams.get('id');

            if (!applicationId) {
                showError('No application ID provided.');
                return;
            }

            loadApplicationDetails(applicationId);
        });

        async function loadApplicationDetails(applicationId) {
            try {
                // This would be a separate API endpoint to get full application details
                // For now, we'll show a placeholder message
                setTimeout(() => {
                    showError('Application details API not yet implemented. This would show full application details for ID: ' + applicationId);
                }, 1000);

            } catch (error) {
                console.error('Error loading application details:', error);
                showError('Failed to load application details.');
            }
        }

        function showError(message) {
            document.getElementById('loadingMessage').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'block';
            document.getElementById('errorMessage').querySelector('p').textContent = message;
        }

        function showApplication(data) {
            document.getElementById('loadingMessage').style.display = 'none';
            document.getElementById('applicationContent').style.display = 'block';
            
            // Populate application data
            // This would be implemented when the API is ready
        }

        async function updateApplicationStatus(applicationId, status) {
            if (!confirm(`Are you sure you want to ${status.toLowerCase()} this application?`)) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('application_id', applicationId);
                formData.append('status', status);

                const response = await fetch('../api/admin_applications.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert('Application status updated successfully!');
                    location.reload(); // Reload to show updated status
                } else {
                    alert('Failed to update status: ' + result.message);
                }
            } catch (error) {
                console.error('Error updating status:', error);
                alert('An error occurred while updating the status.');
            }
        }
    </script>
</body>
</html>
