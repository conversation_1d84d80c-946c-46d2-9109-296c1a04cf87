<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scholarship Application Form - Access Welfare Trust</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <nav class="nav-links">
                    <a href="index.html">Home</a>
                    <a href="about.html">About Us</a>
                    <a href="eligibility.html">Eligibility</a>
                    <a href="application.html" class="active">Apply</a>
                </nav>
                <div id="headerAuth">
                    <a href="login-signup.html" class="admin-link">Login / Sign Up</a>
                </div>
            </div>
        </div>
    </header>

    <main class="application-container">
        <h2>Educational Scholarship Application Form</h2>
        <p class="deadline">Last Date for Applying: 31st August 2025</p>

        <div class="form-progress">
            <div class="step active" data-step="1">
                <div class="step-number">1</div>
                <div class="step-title">Personal Details</div>
            </div>
            <div class="step" data-step="2">
                <div class="step-number">2</div>
                <div class="step-title">Family Details</div>
            </div>
            <div class="step" data-step="3">
                <div class="step-number">3</div>
                <div class="step-title">Educational Details</div>
            </div>
            <div class="step" data-step="4">
                <div class="step-number">4</div>
                <div class="step-title">Scholarship Details</div>
            </div>
            <div class="step" data-step="5">
                <div class="step-number">5</div>
                <div class="step-title">Document Upload</div>
            </div>
        </div>

        <form id="scholarshipForm" enctype="multipart/form-data">
            <!-- Step 1: Personal Details -->
            <div class="form-step" id="step1">
                <h3>Personal Details</h3>
                <div class="photo-upload">
                    <div class="photo-placeholder">
                        <p>Paste Recent Passport Size Photograph here</p>
                        <input type="file" id="photo" name="photo" accept="image/*">
                        <label for="photo">Upload Photo</label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="name">1. Name</label>
                    <input type="text" id="name" name="name" required>
                </div>

                <div class="form-group">
                    <label for="gender">2. Gender</label>
                    <select id="gender" name="gender" required>
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="dob">3. Date of Birth</label>
                    <input type="date" id="dob" name="dob" required>
                    <label for="age" class="inline-label">Age:</label>
                    <input type="number" id="age" name="age" min="1" max="100" required>
                </div>

                <div class="form-group">
                    <label for="birthplace">4. Place of Birth</label>
                    <input type="text" id="birthplace" name="birthplace" required>
                </div>

                <div class="form-group">
                    <label for="maritalStatus">5. Marital Status</label>
                    <select id="maritalStatus" name="maritalStatus" required>
                        <option value="">Select Marital Status</option>
                        <option value="Single">Single</option>
                        <option value="Married">Married</option>
                        <option value="Divorced">Divorced</option>
                        <option value="Widowed">Widowed</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="religion">6. Religion</label>
                    <input type="text" id="religion" name="religion" required>
                </div>

                <div class="form-group">
                    <label for="category">7. Reservation Category</label>
                    <select id="category" name="category" required>
                        <option value="">Select Category</option>
                        <option value="BC">BC</option>
                        <option value="MBC">MBC</option>
                        <option value="SC">SC</option>
                        <option value="ST">ST</option>
                        <option value="General">General</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="nationality">8. Nationality</label>
                    <input type="text" id="nationality" name="nationality" required>
                </div>

                <div class="form-group">
                    <label for="presentAddress">9. Present Address</label>
                    <textarea id="presentAddress" name="presentAddress" rows="3" required></textarea>
                    <div class="address-fields">
                        <div>
                            <label for="presentState">State</label>
                            <input type="text" id="presentState" name="presentState" required>
                        </div>
                        <div>
                            <label for="presentCountry">Country</label>
                            <input type="text" id="presentCountry" name="presentCountry" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="permanentAddress">10. Permanent Address</label>
                    <textarea id="permanentAddress" name="permanentAddress" rows="3" required></textarea>
                    <div class="address-fields">
                        <div>
                            <label for="permanentState">State</label>
                            <input type="text" id="permanentState" name="permanentState" required>
                        </div>
                        <div>
                            <label for="permanentCountry">Country</label>
                            <input type="text" id="permanentCountry" name="permanentCountry" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="phone">11. Contact Phone No.</label>
                    <input type="tel" id="phone" name="phone" required>
                </div>

                <div class="form-group">
                    <label for="whatsapp">12. WhatsApp No.</label>
                    <input type="tel" id="whatsapp" name="whatsapp">
                </div>

                <div class="form-group">
                    <label for="email">13. Email ID</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-navigation">
                    <button type="button" class="btn-next" data-next="2">Save & Continue</button>
                </div>
            </div>

            <!-- Step 2: Family Details -->
            <div class="form-step" id="step2" style="display: none;">
                <h3>Family Details</h3>

                <div class="form-group">
                    <label for="familyDetails">14. Family Details</label>
                    <div class="table-responsive">
                        <table class="family-table">
                            <thead>
                                <tr>
                                    <th>Relationship</th>
                                    <th>Name</th>
                                    <th>Age</th>
                                    <th>Occupation</th>
                                    <th>Annual Income</th>
                                    <th>Details of Employment</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Father</td>
                                    <td><input type="text" name="father_name"></td>
                                    <td><input type="number" name="father_age" min="1" max="120"></td>
                                    <td><input type="text" name="father_occupation"></td>
                                    <td><input type="number" name="father_income" min="0"></td>
                                    <td><input type="text" name="father_employment"></td>
                                </tr>
                                <tr>
                                    <td>Mother</td>
                                    <td><input type="text" name="mother_name"></td>
                                    <td><input type="number" name="mother_age" min="1" max="120"></td>
                                    <td><input type="text" name="mother_occupation"></td>
                                    <td><input type="number" name="mother_income" min="0"></td>
                                    <td><input type="text" name="mother_employment"></td>
                                </tr>
                                <tr>
                                    <td>Spouse</td>
                                    <td><input type="text" name="spouse_name"></td>
                                    <td><input type="number" name="spouse_age" min="1" max="120"></td>
                                    <td><input type="text" name="spouse_occupation"></td>
                                    <td><input type="number" name="spouse_income" min="0"></td>
                                    <td><input type="text" name="spouse_employment"></td>
                                </tr>
                                <tr>
                                    <td>Sibling 1</td>
                                    <td><input type="text" name="sibling1_name"></td>
                                    <td><input type="number" name="sibling1_age" min="1" max="120"></td>
                                    <td><input type="text" name="sibling1_occupation"></td>
                                    <td><input type="number" name="sibling1_income" min="0"></td>
                                    <td><input type="text" name="sibling1_employment"></td>
                                </tr>
                                <tr>
                                    <td>Sibling 2</td>
                                    <td><input type="text" name="sibling2_name"></td>
                                    <td><input type="number" name="sibling2_age" min="1" max="120"></td>
                                    <td><input type="text" name="sibling2_occupation"></td>
                                    <td><input type="number" name="sibling2_income" min="0"></td>
                                    <td><input type="text" name="sibling2_employment"></td>
                                </tr>
                                <tr>
                                    <td>Sibling 3</td>
                                    <td><input type="text" name="sibling3_name"></td>
                                    <td><input type="number" name="sibling3_age" min="1" max="120"></td>
                                    <td><input type="text" name="sibling3_occupation"></td>
                                    <td><input type="number" name="sibling3_income" min="0"></td>
                                    <td><input type="text" name="sibling3_employment"></td>
                                </tr>
                                <tr>
                                    <td>Sibling 4</td>
                                    <td><input type="text" name="sibling4_name"></td>
                                    <td><input type="number" name="sibling4_age" min="1" max="120"></td>
                                    <td><input type="text" name="sibling4_occupation"></td>
                                    <td><input type="number" name="sibling4_income" min="0"></td>
                                    <td><input type="text" name="sibling4_employment"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="form-group">
                    <label for="familyIncome">15. Family Total Annual Income (Please attach Income Certificate)</label>
                    <input type="number" id="familyIncome" name="familyIncome" min="0" required>
                </div>

                <div class="form-group">
                    <label for="isDisabled">16. Is applicant a person with disability? (Yes/No)</label>
                    <select id="isDisabled" name="isDisabled" required>
                        <option value="">Select</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>

                <div id="disabilityDetails" class="form-group" style="display: none;">
                    <label for="disabilityType">17. Type of Disability</label>
                    <div class="table-responsive">
                        <table class="disability-table">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>% of Disability</th>
                                    <th>Describe Disability</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="text" name="disability_type"></td>
                                    <td><input type="number" name="disability_percentage" min="0" max="100"></td>
                                    <td><input type="text" name="disability_description"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="form-group">
                        <label for="disabilityCertificate">18. Disability Certificate</label>
                        <div class="certificate-details">
                            <div>
                                <label for="issuingAuthority">i. Name & designation of Issuing Authority</label>
                                <input type="text" id="issuingAuthority" name="issuingAuthority">
                            </div>
                            <div>
                                <label for="certificateNumber">ii. Certificate Number</label>
                                <input type="text" id="certificateNumber" name="certificateNumber">
                            </div>
                            <div>
                                <label for="issueDate">iii. Date & Place of Issue</label>
                                <input type="text" id="issueDate" name="issueDate">
                            </div>
                        </div>
                        <div class="file-upload">
                            <label for="disabilityCertificateFile">Upload Disability Certificate</label>
                            <input type="file" id="disabilityCertificateFile" name="disabilityCertificateFile">
                        </div>
                    </div>
                </div>

                <div class="form-navigation">
                    <button type="button" class="btn-prev" data-prev="1">Previous</button>
                    <button type="button" class="btn-next" data-next="3">Save & Continue</button>
                </div>
            </div>

            <!-- Step 3: Educational Details -->
            <div class="form-step" id="step3" style="display: none;">
                <h3>Educational Details</h3>

                <div class="form-group">
                    <label for="educationalDetails">19. Educational Details (Please attach all Marks Certificate)</label>
                    <div class="table-responsive">
                        <table class="education-table">
                            <thead>
                                <tr>
                                    <th>Educational details</th>
                                    <th>Name & Address of Institution</th>
                                    <th>Govt / Private</th>
                                    <th>Board / University</th>
                                    <th>Marks / Grade Obtained</th>
                                    <th>Year of Passing</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>X (SSLC)/10th</td>
                                    <td><input type="text" name="sslc_institution"></td>
                                    <td>
                                        <select name="sslc_type">
                                            <option value="">Select</option>
                                            <option value="Government">Government</option>
                                            <option value="Private">Private</option>
                                        </select>
                                    </td>
                                    <td><input type="text" name="sslc_board"></td>
                                    <td><input type="text" name="sslc_marks"></td>
                                    <td><input type="number" name="sslc_year" min="1900" max="2030"></td>
                                </tr>
                                <tr>
                                    <td>XII (HSC)/+2</td>
                                    <td><input type="text" name="hsc_institution"></td>
                                    <td>
                                        <select name="hsc_type">
                                            <option value="">Select</option>
                                            <option value="Government">Government</option>
                                            <option value="Private">Private</option>
                                        </select>
                                    </td>
                                    <td><input type="text" name="hsc_board"></td>
                                    <td><input type="text" name="hsc_marks"></td>
                                    <td><input type="number" name="hsc_year" min="1900" max="2030"></td>
                                </tr>
                                <tr>
                                    <td>UG Degree</td>
                                    <td><input type="text" name="ug_institution"></td>
                                    <td>
                                        <select name="ug_type">
                                            <option value="">Select</option>
                                            <option value="Government">Government</option>
                                            <option value="Private">Private</option>
                                        </select>
                                    </td>
                                    <td><input type="text" name="ug_board"></td>
                                    <td><input type="text" name="ug_marks"></td>
                                    <td><input type="number" name="ug_year" min="1900" max="2030"></td>
                                </tr>
                                <tr>
                                    <td>Vocational</td>
                                    <td><input type="text" name="vocational_institution"></td>
                                    <td>
                                        <select name="vocational_type">
                                            <option value="">Select</option>
                                            <option value="Government">Government</option>
                                            <option value="Private">Private</option>
                                        </select>
                                    </td>
                                    <td><input type="text" name="vocational_board"></td>
                                    <td><input type="text" name="vocational_marks"></td>
                                    <td><input type="number" name="vocational_year" min="1900" max="2030"></td>
                                </tr>
                                <tr>
                                    <td>Diploma</td>
                                    <td><input type="text" name="diploma_institution"></td>
                                    <td>
                                        <select name="diploma_type">
                                            <option value="">Select</option>
                                            <option value="Government">Government</option>
                                            <option value="Private">Private</option>
                                        </select>
                                    </td>
                                    <td><input type="text" name="diploma_board"></td>
                                    <td><input type="text" name="diploma_marks"></td>
                                    <td><input type="number" name="diploma_year" min="1900" max="2030"></td>
                                </tr>
                                <tr>
                                    <td>Others</td>
                                    <td><input type="text" name="others_institution"></td>
                                    <td>
                                        <select name="others_type">
                                            <option value="">Select</option>
                                            <option value="Government">Government</option>
                                            <option value="Private">Private</option>
                                        </select>
                                    </td>
                                    <td><input type="text" name="others_board"></td>
                                    <td><input type="text" name="others_marks"></td>
                                    <td><input type="number" name="others_year" min="1900" max="2030"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="file-upload">
                        <label for="marksheets">Upload All Marksheets</label>
                        <input type="file" id="marksheets" name="marksheets" multiple>
                    </div>
                </div>

                <div class="form-navigation">
                    <button type="button" class="btn-prev" data-prev="2">Previous</button>
                    <button type="button" class="btn-next" data-next="4">Save & Continue</button>
                </div>
            </div>

            <!-- Step 4: Scholarship Details -->
            <div class="form-step" id="step4" style="display: none;">
                <h3>Scholarship Details</h3>

                <div class="section-heading">Programme for which scholarship is applied</div>

                <div class="form-group">
                    <label for="currentCourse">20. Course (Currently studying)</label>
                    <input type="text" id="currentCourse" name="currentCourse" required>
                </div>

                <div class="form-group">
                    <label for="courseDuration">21. Course Duration</label>
                    <input type="text" id="courseDuration" name="courseDuration" required>
                </div>

                <div class="form-group">
                    <label for="courseYear">22. Which Year of the Course</label>
                    <input type="text" id="courseYear" name="courseYear" required>
                </div>

                <div class="form-group">
                    <label for="rollNo">23. Roll No.</label>
                    <input type="text" id="rollNo" name="rollNo" required>
                </div>

                <div class="form-group">
                    <label for="institutionName">24. Name of Institution</label>
                    <input type="text" id="institutionName" name="institutionName" required>
                </div>

                <div class="form-group">
                    <label for="institutionType">25. Type of Institution</label>
                    <select id="institutionType" name="institutionType" required>
                        <option value="">Select</option>
                        <option value="Government">Government</option>
                        <option value="Aided">Aided</option>
                        <option value="Private">Private</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="institutionAddress">26. Address of the Institution</label>
                    <textarea id="institutionAddress" name="institutionAddress" rows="3" required></textarea>
                </div>

                <div class="form-group">
                    <label for="institutionPhone">27. Phone Number of Institution</label>
                    <input type="tel" id="institutionPhone" name="institutionPhone" required>
                </div>

                <div class="form-group">
                    <label for="institutionEmail">Email ID of Institution</label>
                    <input type="email" id="institutionEmail" name="institutionEmail" required>
                </div>

                <div class="form-group">
                    <label for="institutionWebsite">Website of Institution</label>
                    <input type="url" id="institutionWebsite" name="institutionWebsite">
                </div>

                <div class="form-group">
                    <label for="termFees">28. Term/Semester for which Fees to be paid</label>
                    <input type="text" id="termFees" name="termFees" required>
                </div>

                <div class="form-group">
                    <label for="feeAmount">29. Amount of Fees to be paid</label>
                    <div class="fee-details">
                        <div>
                            <label for="tuitionFees">i) Tuition Fees</label>
                            <input type="number" id="tuitionFees" name="tuitionFees" min="0" required>
                        </div>
                        <div>
                            <label for="otherFees">ii) Other Fees</label>
                            <input type="number" id="otherFees" name="otherFees" min="0" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="feeCircular">Enclose Institute official Fee Circular or Fee Notification</label>
                    <input type="file" id="feeCircular" name="feeCircular" required>
                </div>

                <div class="form-group">
                    <label for="scholarshipAmount">30. Scholarship Amount requested</label>
                    <div>
                        <label for="amountFigures">Amount in Figures</label>
                        <input type="number" id="amountFigures" name="amountFigures" min="0" required>
                    </div>
                    <div>
                        <label for="amountWords">Amount in Words</label>
                        <input type="text" id="amountWords" name="amountWords" required>
                    </div>
                </div>

                <div class="form-group">
                    <label>31. Details of any scholarship received</label>
                    <div>
                        <label>a) Did you receive AWT scholarship in the Academic Year 2024-25?</label>
                        <div class="radio-group">
                            <label>
                                <input type="radio" name="previousAWTScholarship" value="Yes"> Yes
                            </label>
                            <label>
                                <input type="radio" name="previousAWTScholarship" value="No"> No
                            </label>
                        </div>
                    </div>
                    <div>
                        <label for="otherScholarships">b) Other scholarships details</label>
                        <input type="text" id="otherScholarships" name="otherScholarships">
                    </div>
                </div>

                <div class="form-group">
                    <label for="appliedScholarships">32. Details of other scholarships applied for</label>
                    <textarea id="appliedScholarships" name="appliedScholarships" rows="2"></textarea>
                </div>

                <div class="form-navigation">
                    <button type="button" class="btn-prev" data-prev="3">Previous</button>
                    <button type="button" class="btn-next" data-next="5">Save & Continue</button>
                </div>
            </div>

            <!-- Step 5: Document Upload and Declaration -->
            <div class="form-step" id="step5" style="display: none;">
                <h3>References, Documents & Declaration</h3>

                <div class="form-group">
                    <label for="references">33. Provide contacts of two references (At least one reference from educational institution)</label>
                    <div class="table-responsive">
                        <table class="references-table">
                            <thead>
                                <tr>
                                    <th>Name of Referring Person</th>
                                    <th>Phone #</th>
                                    <th>Email ID</th>
                                    <th>Teacher / HoD / Principal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="text" name="reference1_name" required></td>
                                    <td><input type="tel" name="reference1_phone" required></td>
                                    <td><input type="email" name="reference1_email" required></td>
                                    <td>
                                        <select name="reference1_position" required>
                                            <option value="">Select</option>
                                            <option value="Teacher">Teacher</option>
                                            <option value="HoD">HoD</option>
                                            <option value="Principal">Principal</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="text" name="reference2_name" required></td>
                                    <td><input type="tel" name="reference2_phone" required></td>
                                    <td><input type="email" name="reference2_email" required></td>
                                    <td>
                                        <select name="reference2_position" required>
                                            <option value="">Select</option>
                                            <option value="Teacher">Teacher</option>
                                            <option value="HoD">HoD</option>
                                            <option value="Principal">Principal</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="form-group">
                    <label for="extracurricular">34. Details of Extracurricular/Sports accomplishments</label>
                    <textarea id="extracurricular" name="extracurricular" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="otherInfo">35. Any other information or requests</label>
                    <textarea id="otherInfo" name="otherInfo" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="goals">36. Write a short note about your goals in life & why you need this scholarship.</label>
                    <textarea id="goals" name="goals" rows="6" required></textarea>
                </div>

                <div class="form-group">
                    <h4>Self Declaration</h4>
                    <div class="declaration">
                        <p>I assure that I am not a beneficiary of any other scholrship either from Government or from any Trusts. I hereby declare that the above entries are true to the best of my knowledge and belief, and furnishing of false information by me will result in disqualification of my application.</p>
                    </div>
                    <div class="signature-fields">
                        <div>
                            <label for="place">Place</label>
                            <input type="text" id="place" name="place" required>
                        </div>
                        <div>
                            <label for="date">Date</label>
                            <input type="date" id="date" name="date" required>
                        </div>
                        <div>
                            <label for="signature">Signature</label>
                            <input type="file" id="signature" name="signature" accept="image/*" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <h4>Required Documents</h4>
                    <p class="document-note">All the following documents must be enclosed with application. If any document is unavailable at the time of applying, kindly inform the probable date of submission.</p>
                    <div class="document-uploads">
                        <div class="document-item">
                            <label for="photoID">1. Photo ID</label>
                            <input type="file" id="photoID" name="photoID" required>
                        </div>
                        <div class="document-item">
                            <label for="instituteID">2. Institute ID card</label>
                            <input type="file" id="instituteID" name="instituteID" required>
                        </div>
                        <div class="document-item">
                            <label for="addressProof">3. Address Proof</label>
                            <input type="file" id="addressProof" name="addressProof" required>
                        </div>
                        <div class="document-item">
                            <label for="aadhaarCard">4. Aadhaar Card</label>
                            <input type="file" id="aadhaarCard" name="aadhaarCard" required>
                        </div>
                        <div class="document-item">
                            <label for="birthCertificate">5. Birth Certificate</label>
                            <input type="file" id="birthCertificate" name="birthCertificate" required>
                        </div>
                        <div class="document-item">
                            <label for="communityCertificate">6. Community Certificate</label>
                            <input type="file" id="communityCertificate" name="communityCertificate" required>
                        </div>
                        <div class="document-item">
                            <label for="courseDetails">9. Course Fee details issued by Institution</label>
                            <input type="file" id="courseDetails" name="courseDetails" required>
                        </div>
                        <div class="document-item">
                            <label for="incomeCertificate">10. Income Certificate</label>
                            <input type="file" id="incomeCertificate" name="incomeCertificate" required>
                        </div>
                        <div class="document-item">
                            <label for="attendanceCertificate">11. Attendance Certificate</label>
                            <input type="file" id="attendanceCertificate" name="attendanceCertificate">
                        </div>
                        <div class="document-item">
                            <label for="feeReceipts">12. Fee Receipts for Academic Year 2025-26</label>
                            <input type="file" id="feeReceipts" name="feeReceipts">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <label>
                            <input type="checkbox" name="termsAgreed" required>
                            I agree to the terms and conditions and confirm that all information provided is accurate.
                        </label>
                    </div>
                </div>

                <div class="form-navigation">
                    <button type="button" class="btn-prev" data-prev="4">Previous</button>
                    <button type="submit" class="btn-submit">Submit Application</button>
                </div>
            </div>
        </form>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <p>Access Welfare Trust is dedicated to supporting education for persons with disabilities and those from economically weaker sections.</p>
                <div class="social-links">
                    <a href="https://www.facebook.com/accesswelfaretrust.gct/"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="https://www.instagram.com/accesswelfaretrust/"><i class="fab fa-instagram"></i></a>
                    <a href="https://www.linkedin.com/company/access-welfare-trust/?viewAsMember=true"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="eligibility.html">Eligibility</a></li>
                    <li><a href="application.html">Apply Now</a></li>
                    <li><a href="current-projects.html">Current Projects</a></li>
                    <li><a href="accomplished-projects.html">Accomplished Projects</a></li>
                    <li><a href="gallery.html">Gallery</a></li>
                    <li><a href="get-involved.html">Get Involved</a></li>
                    <li><a href="login-signup.html">Login / Sign Up</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Contact Us</h3>
                <p><i class="fas fa-map-marker-alt"></i> 1/3, Vasanth Nagar, Behind Jeya Nagar, Karumandapam, Tiruchirappalli - 620001</p>
                <p><i class="fas fa-phone"></i> +91 9498980331</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-globe"></i> www.awt.org.in</p>
            </div>
        </div>

        <div class="copyright">
            <p>&copy; 2025 Access Welfare Trust. All rights reserved. | Registration No: 18/Trichy [1]/BK-4/06/2021</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', function() {
            const userSession = localStorage.getItem('userSession');
            const headerAuth = document.getElementById('headerAuth');

            if (userSession) {
                const user = JSON.parse(userSession);
                headerAuth.innerHTML = `
                    <span style="margin-right: 1rem; color: var(--primary-color);">Welcome, ${user.full_name.split(' ')[0]}</span>
                    <button onclick="logout()" class="admin-link" style="background: #dc3545; border: none; cursor: pointer;">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                `;

                // Pre-fill email field if available
                const emailField = document.getElementById('email');
                if (emailField && user.email) {
                    emailField.value = user.email;
                    emailField.readOnly = true;
                }

                // Pre-fill name field if available
                const nameField = document.getElementById('name');
                if (nameField && user.full_name) {
                    nameField.value = user.full_name;
                }
            } else {
                // Redirect to login if not authenticated
                alert('Please login to access the application form.');
                window.location.href = 'login-signup.html';
            }
        });

        function logout() {
            fetch('api/logout.php', { method: 'POST' })
                .then(() => {
                    localStorage.removeItem('userSession');
                    window.location.href = 'login-signup.html';
                })
                .catch(() => {
                    localStorage.removeItem('userSession');
                    window.location.href = 'login-signup.html';
                });
        }
    </script>
</body>
</html>
