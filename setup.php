<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - Access Welfare Trust</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .step { margin: 1rem 0; padding: 1rem; border-left: 4px solid #007bff; background: #f8f9fa; }
        button {
            background: #007bff;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 0.5rem 0;
        }
        button:hover { background: #0056b3; }
        .config-form {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .form-group {
            margin: 1rem 0;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Access Welfare Trust - Application Setup</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            if ($action === 'create_config') {
                // Create database configuration
                $dbHost = $_POST['db_host'] ?? 'localhost';
                $dbName = $_POST['db_name'] ?? 'awt_scholarship';
                $dbUser = $_POST['db_user'] ?? 'root';
                $dbPass = $_POST['db_pass'] ?? '';
                
                $configContent = "<?php
// Database configuration
define('DB_HOST', '{$dbHost}');
define('DB_USERNAME', '{$dbUser}');
define('DB_PASSWORD', '{$dbPass}');
define('DB_NAME', '{$dbName}');

// Create connection
function getDBConnection() {
    try {
        \$pdo = new PDO(
            \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\",
            DB_USERNAME,
            DB_PASSWORD,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );
        return \$pdo;
    } catch (PDOException \$e) {
        error_log(\"Database connection failed: \" . \$e->getMessage());
        throw new Exception(\"Database connection failed\");
    }
}

// Test database connection
function testDBConnection() {
    try {
        \$pdo = getDBConnection();
        return true;
    } catch (Exception \$e) {
        return false;
    }
}
?>";
                
                if (file_put_contents('config/database.php', $configContent)) {
                    echo '<div class="success">✓ Database configuration created successfully!</div>';
                } else {
                    echo '<div class="error">✗ Failed to create database configuration file.</div>';
                }
            }
            
            if ($action === 'test_connection') {
                if (file_exists('config/database.php')) {
                    require_once 'config/database.php';
                    if (testDBConnection()) {
                        echo '<div class="success">✓ Database connection successful!</div>';
                    } else {
                        echo '<div class="error">✗ Database connection failed. Please check your configuration.</div>';
                    }
                } else {
                    echo '<div class="error">✗ Database configuration file not found.</div>';
                }
            }
            
            if ($action === 'create_database') {
                try {
                    $dbHost = $_POST['db_host'] ?? 'localhost';
                    $dbUser = $_POST['db_user'] ?? 'root';
                    $dbPass = $_POST['db_pass'] ?? '';
                    $dbName = $_POST['db_name'] ?? 'awt_scholarship';
                    
                    // Connect without database name first
                    $pdo = new PDO("mysql:host={$dbHost};charset=utf8mb4", $dbUser, $dbPass);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // Create database
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}`");
                    echo '<div class="success">✓ Database created successfully!</div>';
                    
                    // Use the database
                    $pdo->exec("USE `{$dbName}`");
                    
                    // Read and execute schema
                    $schema = file_get_contents('database/schema.sql');
                    if ($schema) {
                        // Remove the CREATE DATABASE and USE statements from schema
                        $schema = preg_replace('/CREATE DATABASE.*?;/', '', $schema);
                        $schema = preg_replace('/USE.*?;/', '', $schema);
                        
                        $pdo->exec($schema);
                        echo '<div class="success">✓ Database tables created successfully!</div>';
                        echo '<div class="success">✓ Default admin user created (email: <EMAIL>, password: password)</div>';
                        echo '<div class="warning">⚠ Please change the default admin password after first login!</div>';
                    } else {
                        echo '<div class="error">✗ Could not read schema file.</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="error">✗ Database setup failed: ' . $e->getMessage() . '</div>';
                }
            }
        }
        ?>
        
        <div class="step">
            <h3>Step 1: Database Configuration</h3>
            <p>Configure your database connection settings:</p>
            
            <form method="POST" class="config-form">
                <input type="hidden" name="action" value="create_config">
                
                <div class="form-group">
                    <label for="db_host">Database Host:</label>
                    <input type="text" id="db_host" name="db_host" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label for="db_name">Database Name:</label>
                    <input type="text" id="db_name" name="db_name" value="awt_scholarship" required>
                </div>
                
                <div class="form-group">
                    <label for="db_user">Database Username:</label>
                    <input type="text" id="db_user" name="db_user" value="root" required>
                </div>
                
                <div class="form-group">
                    <label for="db_pass">Database Password:</label>
                    <input type="password" id="db_pass" name="db_pass">
                </div>
                
                <button type="submit">Create Configuration</button>
            </form>
        </div>
        
        <div class="step">
            <h3>Step 2: Test Database Connection</h3>
            <p>Test if the database connection is working:</p>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_connection">
                <button type="submit">Test Connection</button>
            </form>
        </div>
        
        <div class="step">
            <h3>Step 3: Create Database and Tables</h3>
            <p>Create the database and all required tables:</p>
            
            <form method="POST" class="config-form">
                <input type="hidden" name="action" value="create_database">
                
                <div class="form-group">
                    <label for="setup_db_host">Database Host:</label>
                    <input type="text" id="setup_db_host" name="db_host" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label for="setup_db_name">Database Name:</label>
                    <input type="text" id="setup_db_name" name="db_name" value="awt_scholarship" required>
                </div>
                
                <div class="form-group">
                    <label for="setup_db_user">Database Username:</label>
                    <input type="text" id="setup_db_user" name="db_user" value="root" required>
                </div>
                
                <div class="form-group">
                    <label for="setup_db_pass">Database Password:</label>
                    <input type="password" id="setup_db_pass" name="db_pass">
                </div>
                
                <button type="submit">Create Database & Tables</button>
            </form>
        </div>
        
        <div class="step">
            <h3>Step 4: Complete Setup</h3>
            <p>Once all steps are completed successfully:</p>
            <ul>
                <li>Delete this setup.php file for security</li>
                <li>Access your application at <a href="index.html">index.html</a></li>
                <li>Login as admin with email: <strong><EMAIL></strong> and password: <strong>password</strong></li>
                <li>Change the default admin password immediately</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>Requirements Check</h3>
            <ul>
                <li>PHP Version: <?php echo phpversion(); ?> <?php echo version_compare(phpversion(), '7.4.0', '>=') ? '<span class="success">✓</span>' : '<span class="error">✗ (7.4+ required)</span>'; ?></li>
                <li>PDO Extension: <?php echo extension_loaded('pdo') ? '<span class="success">✓</span>' : '<span class="error">✗</span>'; ?></li>
                <li>PDO MySQL: <?php echo extension_loaded('pdo_mysql') ? '<span class="success">✓</span>' : '<span class="error">✗</span>'; ?></li>
                <li>Config Directory: <?php echo is_writable('config') ? '<span class="success">✓ Writable</span>' : '<span class="error">✗ Not writable</span>'; ?></li>
            </ul>
        </div>
    </div>
</body>
</html>
